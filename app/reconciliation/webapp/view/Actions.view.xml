<mvc:View controllerName="reconciliation.controller.Actions"
    xmlns:mvc="sap.ui.core.mvc"
    xmlns="sap.m"
    xmlns:f="sap.f"
    xmlns:core="sap.ui.core">
    <Page id="actionsPage" title="{i18n>actionsTitle}" showNavButton="true" navButtonPress="onNavBack">
        <content>
            <ScrollContainer id="actionsScrollContainer" height="100%" width="100%" horizontal="false" vertical="true">
                
                <!-- Actions Overview Header -->
                <VBox id="headerSection" class="sapUiMediumMargin">
                    <Title id="headerTitle" text="Actions Tracking & Monitoring" level="H2" class="sapUiMediumMarginBottom"/>
                    <MessageStrip id="actionsInfoStrip" 
                        text="Monitor and track the execution status of all reconciliation actions including stock reallocations, procurement requests, and production orders."
                        type="Information"
                        class="sapUiMediumMarginBottom"/>
                </VBox>

                <!-- KPI Tiles Section -->
                <VBox id="kpiSection" class="sapUiMediumMargin">
                    <Title id="kpiTitle" text="Action Status Overview" level="H3" class="sapUiMediumMarginBottom"/>
                    <f:GridContainer id="kpiGridContainer" class="sapUiResponsiveMargin">
                        <f:GridContainerSettings rowSize="5rem" columnSize="5rem" gap="1rem"/>
                        
                        <GenericTile id="totalActionsTile" class="sapUiTinyMargin" press="onKPITilePress">
                            <TileContent id="totalActionsTileContent">
                                <NumericContent id="totalActionsContent" 
                                    value="{actionsModel>/kpis/totalActions}" 
                                    valueColor="Good"
                                    indicator="None"/>
                            </TileContent>
                            <tileContent>
                                <TileContent id="totalActionsTextContent">
                                    <content>
                                        <Text id="totalActionsText" text="Total Actions"/>
                                    </content>
                                </TileContent>
                            </tileContent>
                        </GenericTile>

                        <GenericTile id="pendingActionsTile" class="sapUiTinyMargin" press="onKPITilePress">
                            <TileContent id="pendingActionsTileContent">
                                <NumericContent id="pendingActionsContent" 
                                    value="{actionsModel>/kpis/pendingActions}" 
                                    valueColor="Critical"
                                    indicator="Up"/>
                            </TileContent>
                            <tileContent>
                                <TileContent id="pendingActionsTextContent">
                                    <content>
                                        <Text id="pendingActionsText" text="Pending Actions"/>
                                    </content>
                                </TileContent>
                            </tileContent>
                        </GenericTile>

                        <GenericTile id="inProgressActionsTile" class="sapUiTinyMargin" press="onKPITilePress">
                            <TileContent id="inProgressActionsTileContent">
                                <NumericContent id="inProgressActionsContent" 
                                    value="{actionsModel>/kpis/inProgressActions}" 
                                    valueColor="Error"
                                    indicator="None"/>
                            </TileContent>
                            <tileContent>
                                <TileContent id="inProgressActionsTextContent">
                                    <content>
                                        <Text id="inProgressActionsText" text="In Progress"/>
                                    </content>
                                </TileContent>
                            </tileContent>
                        </GenericTile>

                        <GenericTile id="completedActionsTile" class="sapUiTinyMargin" press="onKPITilePress">
                            <TileContent id="completedActionsTileContent">
                                <NumericContent id="completedActionsContent" 
                                    value="{actionsModel>/kpis/completedActions}" 
                                    valueColor="Good"
                                    indicator="Up"/>
                            </TileContent>
                            <tileContent>
                                <TileContent id="completedActionsTextContent">
                                    <content>
                                        <Text id="completedActionsText" text="Completed"/>
                                    </content>
                                </TileContent>
                            </tileContent>
                        </GenericTile>

                        <GenericTile id="successRateTile" class="sapUiTinyMargin" press="onKPITilePress">
                            <TileContent id="successRateTileContent">
                                <NumericContent id="successRateContent" 
                                    value="{path: 'actionsModel>/kpis/successRate', formatter: '.formatPercentage'}" 
                                    valueColor="Good"
                                    indicator="Up"/>
                            </TileContent>
                            <tileContent>
                                <TileContent id="successRateTextContent">
                                    <content>
                                        <Text id="successRateText" text="Success Rate"/>
                                    </content>
                                </TileContent>
                            </tileContent>
                        </GenericTile>

                        <GenericTile id="avgExecutionTimeTile" class="sapUiTinyMargin" press="onKPITilePress">
                            <TileContent id="avgExecutionTimeTileContent">
                                <NumericContent id="avgExecutionTimeContent" 
                                    value="{path: 'actionsModel>/kpis/avgExecutionTime', formatter: '.formatDuration'}" 
                                    valueColor="Neutral"
                                    indicator="None"/>
                            </TileContent>
                            <tileContent>
                                <TileContent id="avgExecutionTimeTextContent">
                                    <content>
                                        <Text id="avgExecutionTimeText" text="Avg. Execution Time"/>
                                    </content>
                                </TileContent>
                            </tileContent>
                        </GenericTile>
                    </f:GridContainer>
                </VBox>

                <!-- Filter and Control Section -->
                <VBox id="controlSection" class="sapUiMediumMargin">
                    <Panel id="controlPanel" headerText="Action Filters" class="sapUiResponsiveMargin">
                        <content>
                            <HBox id="filterBar" class="sapUiMediumMargin">
                                <ComboBox id="actionTypeFilter" 
                                    placeholder="Filter by Action Type"
                                    selectedKey="{actionsModel>/filters/actionType}"
                                    selectionChange="onFilterChange"
                                    class="sapUiTinyMarginEnd">
                                    <core:Item key="" text="All Action Types"/>
                                    <core:Item key="STOCK_REALLOCATION" text="Stock Reallocation"/>
                                    <core:Item key="PROCUREMENT_REQUEST" text="Procurement Request"/>
                                    <core:Item key="PRODUCTION_ORDER" text="Production Order"/>
                                </ComboBox>
                                <ComboBox id="statusFilter" 
                                    placeholder="Filter by Status"
                                    selectedKey="{actionsModel>/filters/status}"
                                    selectionChange="onFilterChange"
                                    class="sapUiTinyMarginEnd">
                                    <core:Item key="" text="All Status"/>
                                    <core:Item key="PENDING" text="Pending"/>
                                    <core:Item key="IN_PROGRESS" text="In Progress"/>
                                    <core:Item key="COMPLETED" text="Completed"/>
                                    <core:Item key="FAILED" text="Failed"/>
                                    <core:Item key="CANCELLED" text="Cancelled"/>
                                </ComboBox>
                                <ComboBox id="priorityFilter" 
                                    placeholder="Filter by Priority"
                                    selectedKey="{actionsModel>/filters/priority}"
                                    selectionChange="onFilterChange"
                                    class="sapUiTinyMarginEnd">
                                    <core:Item key="" text="All Priorities"/>
                                    <core:Item key="HIGH" text="High Priority"/>
                                    <core:Item key="MEDIUM" text="Medium Priority"/>
                                    <core:Item key="LOW" text="Low Priority"/>
                                </ComboBox>
                                <DateRangeSelection id="dateRangeFilter" 
                                    placeholder="Filter by Date Range"
                                    change="onDateRangeChange"
                                    class="sapUiTinyMarginEnd"/>
                                <Button id="clearFiltersButton" 
                                    text="{i18n>clearFilters}" 
                                    icon="sap-icon://clear-filter"
                                    type="Transparent" 
                                    press="onClearFilters"
                                    class="sapUiTinyMarginEnd"/>
                                <Button id="refreshActionsButton" 
                                    text="{i18n>refresh}" 
                                    icon="sap-icon://refresh"
                                    type="Default" 
                                    press="onRefreshActions"/>
                            </HBox>
                        </content>
                    </Panel>
                </VBox>

                <!-- Quick Actions Section -->
                <VBox id="quickActionsSection" class="sapUiMediumMargin">
                    <Panel id="quickActionsPanel" headerText="Quick Actions" class="sapUiResponsiveMargin">
                        <content>
                            <HBox id="quickActionsBar" class="sapUiMediumMargin">
                                <Button id="createStockReallocationButton" 
                                    text="Create Stock Reallocation" 
                                    icon="sap-icon://inventory"
                                    type="Default" 
                                    press="onCreateStockReallocation"
                                    class="sapUiTinyMarginEnd"/>
                                <Button id="createProcurementRequestButton" 
                                    text="Create Procurement Request" 
                                    icon="sap-icon://cart"
                                    type="Default" 
                                    press="onCreateProcurementRequest"
                                    class="sapUiTinyMarginEnd"/>
                                <Button id="createProductionOrderButton" 
                                    text="Create Production Order" 
                                    icon="sap-icon://factory"
                                    type="Default" 
                                    press="onCreateProductionOrder"
                                    class="sapUiTinyMarginEnd"/>
                                <Button id="bulkUpdateStatusButton" 
                                    text="Bulk Update Status" 
                                    icon="sap-icon://batch-payments"
                                    type="Default" 
                                    press="onBulkUpdateStatus"
                                    enabled="{actionsModel>/hasSelectedActions}"/>
                            </HBox>
                        </content>
                    </Panel>
                </VBox>

                <!-- Actions Table -->
                <VBox id="actionsTableSection" class="sapUiMediumMargin">
                    <Table id="actionsTable"
                        items="{actionsModel>/actions}"
                        mode="MultiSelect"
                        selectionChange="onSelectionChange"
                        class="sapUiResponsiveMargin"
                        growing="true"
                        growingThreshold="50">
                        <headerToolbar>
                            <Toolbar id="actionsToolbar">
                                <Title id="actionsTableTitle" text="Actions ({actionsModel>/actions/length})"/>
                                <ToolbarSpacer id="actionsToolbarSpacer"/>
                                <Button id="exportActionsButton" 
                                    text="{i18n>export}" 
                                    icon="sap-icon://excel-attachment"
                                    type="Transparent" 
                                    press="onExportActions"/>
                            </Toolbar>
                        </headerToolbar>
                        <columns>
                            <Column id="actionIdColumn" sortProperty="actionId">
                                <Text id="actionIdColumnHeader" text="Action ID"/>
                            </Column>
                            <Column id="actionTypeColumn" sortProperty="actionType">
                                <Text id="actionTypeColumnHeader" text="Type"/>
                            </Column>
                            <Column id="plantColumn" sortProperty="plant/plantName">
                                <Text id="plantColumnHeader" text="Plant"/>
                            </Column>
                            <Column id="productColumn" sortProperty="product/productName">
                                <Text id="productColumnHeader" text="Product"/>
                            </Column>
                            <Column id="descriptionColumn">
                                <Text id="descriptionColumnHeader" text="Description"/>
                            </Column>
                            <Column id="priorityColumn" sortProperty="priority">
                                <Text id="priorityColumnHeader" text="Priority"/>
                            </Column>
                            <Column id="statusColumn" sortProperty="status">
                                <Text id="statusColumnHeader" text="Status"/>
                            </Column>
                            <Column id="progressColumn" sortProperty="progress">
                                <Text id="progressColumnHeader" text="Progress"/>
                            </Column>
                            <Column id="createdAtColumn" sortProperty="createdAt">
                                <Text id="createdAtColumnHeader" text="Created"/>
                            </Column>
                            <Column id="actionsColumn">
                                <Text id="actionsColumnHeader" text="Actions"/>
                            </Column>
                        </columns>
                        <items>
                            <ColumnListItem id="actionListItem" press="onActionItemPress">
                                <Link id="actionIdLink" text="{actionsModel>actionId}" press="onActionItemPress"/>
                                <ObjectStatus id="actionTypeStatus" 
                                    text="{path: 'actionsModel>actionType', formatter: '.formatActionType'}" 
                                    state="{path: 'actionsModel>actionType', formatter: '.formatActionTypeState'}"/>
                                <Text id="plantText" text="{actionsModel>plant/plantName}"/>
                                <Text id="productText" text="{actionsModel>product/productName}"/>
                                <Text id="descriptionText" text="{actionsModel>description}" maxLines="2"/>
                                <ObjectStatus id="priorityStatus" 
                                    text="{actionsModel>priority}" 
                                    state="{path: 'actionsModel>priority', formatter: '.formatPriorityState'}"/>
                                <ObjectStatus id="statusObject" 
                                    text="{actionsModel>status}" 
                                    state="{path: 'actionsModel>status', formatter: '.formatStatusState'}"/>
                                <ProgressIndicator id="progressIndicator" 
                                    percentValue="{actionsModel>progress}" 
                                    displayValue="{path: 'actionsModel>progress', formatter: '.formatProgressDisplay'}"
                                    state="{path: 'actionsModel>progress', formatter: '.formatProgressState'}"
                                    width="100px"/>
                                <Text id="createdAtText" text="{path: 'actionsModel>createdAt', formatter: '.formatDate'}"/>
                                <HBox id="actionButtons">
                                    <Button id="viewDetailsButton" 
                                        icon="sap-icon://detail-view" 
                                        type="Transparent" 
                                        press="onViewActionDetails"
                                        tooltip="View Details"/>
                                    <Button id="updateStatusButton" 
                                        icon="sap-icon://edit" 
                                        type="Transparent" 
                                        press="onUpdateActionStatus"
                                        tooltip="Update Status"/>
                                    <Button id="cancelActionButton" 
                                        icon="sap-icon://cancel" 
                                        type="Transparent" 
                                        press="onCancelAction"
                                        tooltip="Cancel Action"
                                        visible="{= ${actionsModel>status} === 'PENDING' || ${actionsModel>status} === 'IN_PROGRESS'}"/>
                                </HBox>
                            </ColumnListItem>
                        </items>
                    </Table>
                </VBox>
            </ScrollContainer>
        </content>
    </Page>
</mvc:View>
